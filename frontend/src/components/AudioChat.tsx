import { useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

interface ReverbSocket {
  subscribe: (channel: string) => {
    listen: (event: string, callback: (e: any) => void) => void;
  };
}

declare global {
  interface Window {
    Echo: {
      connector: ReverbSocket;
    };
  }
}

export default function AudioChat() {
  const [isRecording, setIsRecording] = useState(false);
  const [sessionId] = useState(uuidv
