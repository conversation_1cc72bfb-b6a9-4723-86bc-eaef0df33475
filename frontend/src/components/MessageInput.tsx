'use client';

import { useState, useRef } from 'react';

interface MessageInputProps {
  onSendMessage: (text: string, images?: File[]) => void;
  isProcessing: boolean;
  onFocusChange?: (isFocused: boolean) => void;
  streamTranscription?: string;
}

const MessageInput = ({ onSendMessage, isProcessing, onFocusChange, streamTranscription }: MessageInputProps) => {
  const [message, setMessage] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Görsel seçme (birden çok)
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);

    if (files.length === 0) return;

    // Maksimum 5 görsel
    if (selectedImages.length + files.length > 5) {
      alert('Maksimum 5 görsel seçebilirsiniz.');
      return;
    }

    const validFiles: File[] = [];
    const newPreviews: string[] = [];

    files.forEach(file => {
      // Dosya boyutu kontrolü (10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert(`${file.name} dosyası çok büyük. Maksimum 10MB olmalıdır.`);
        return;
      }

      // Dosya tipi kontrolü
      if (!file.type.startsWith('image/') && file.type !== 'application/octet-stream') {
        alert(`${file.name} geçerli bir görsel dosyası değil.`);
        return;
      }

      validFiles.push(file);

      // Preview oluştur
      const reader = new FileReader();
      reader.onload = (e) => {
        newPreviews.push(e.target?.result as string);
        if (newPreviews.length === validFiles.length) {
          setImagePreviews(prev => [...prev, ...newPreviews]);
        }
      };
      reader.readAsDataURL(file);
    });

    setSelectedImages(prev => [...prev, ...validFiles]);
  };

  // Görseli kaldır
  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));

    // Eğer tüm görseller kaldırıldıysa input'u temizle
    if (selectedImages.length === 1) {
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Mesaj gönder
  const handleSend = () => {
    if ((!message.trim() && selectedImages.length === 0) || isProcessing) {
      return;
    }

    onSendMessage(message.trim(), selectedImages.length > 0 ? selectedImages : undefined);

    // Formu temizle
    setMessage('');
    setSelectedImages([]);
    setImagePreviews([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Enter tuşu ile gönder
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="border-t border-white/10 p-4 space-y-2">
      {/* Image Previews */}
      {imagePreviews.length > 0 && (
        <div className="mb-3 flex flex-wrap gap-2">
          {imagePreviews.map((preview, index) => (
            <div key={index} className="relative inline-block">
              <img
                src={preview}
                alt={`Preview ${index + 1}`}
                className="max-w-32 max-h-32 rounded-lg border border-white/20"
              />
              <button
                onClick={() => removeImage(index)}
                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm transition-colors"
                title="Görseli kaldır"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Stream Transcription */}
      {streamTranscription && (
        <div className="bg-blue-600/20 backdrop-blur-sm rounded-lg p-2">
          <p className="text-sm text-blue-100">{streamTranscription}</p>
        </div>
      )}

      {/* Input Area */}
      <div className="flex items-center space-x-2">
        {/* Text Input */}
        <div className="flex-1 relative">
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            onFocus={() => onFocusChange?.(true)}
            onBlur={() => onFocusChange?.(false)}
            placeholder={selectedImages.length > 0 ? "Görseller hakkında bir şey söylemek ister misiniz?" : "Mesajınızı yazın..."}
            className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={1}
            style={{
              minHeight: '48px',
              maxHeight: '120px',
            }}
            disabled={isProcessing}
          />
        </div>

        {/* Image Upload Button */}
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={isProcessing}
          className="p-3 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          title="Görsel ekle"
        >
          <svg className="w-5 h-5 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </button>

        {/* Send Button */}
        <button
          onClick={handleSend}
          disabled={(!message.trim() && selectedImages.length === 0) || isProcessing}
          className="p-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          title="Gönder"
        >
          {isProcessing ? (
            <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          )}
        </button>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={handleImageSelect}
        className="hidden"
      />

      {/* Help Text */}
      <div className="mt-2 text-xs text-gray-500">
        <span>Enter ile gönder</span>
        {selectedImages.length > 0 && (
          <span className="ml-3">• {selectedImages.length} görsel seçildi</span>
        )}
      </div>
    </div>
  );
};

export default MessageInput;
