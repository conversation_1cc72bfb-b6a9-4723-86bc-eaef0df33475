.voice-chatbot {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.voice-chatbot-layout {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  box-sizing: border-box;
  padding: 1rem;
  gap: 1rem;
}

.left-panel {
  display: flex;
  flex-direction: column;
  width: 320px;
  min-width: 220px;
  max-width: 400px;
  height: calc(100vh - 2rem);
  min-height: 0;
  flex-shrink: 0;
  gap: 1rem;
}

.voice-panel-container {
  flex-shrink: 0;
  height: auto;
  min-height: 200px;
  border-radius: 12px;
  overflow: hidden;
}

.chat-panel-container {
  flex: 1;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  max-height: 100%;
}

.right-panel {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  gap: 1rem;
}

.conversation-history {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.main-panel {
  flex: 1;
  height: calc(100vh - 2rem);
  overflow: hidden;
  min-width: 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.history-messages {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
}

.history-message {
  padding: 1rem;
  border-radius: 8px;
  background: #f8f9fa;
  max-width: 100%;
  word-wrap: break-word;
}

.history-message.user {
  border: 1px solid #bbdefb;
  background: #f8f9fa;
}

.history-message.assistant {
  background: #1a2942;
  color: #ffffff;
  border: none;
}

.history-message strong {
  display: block;
  margin-bottom: 0.5rem;
  color: inherit;
  font-weight: 600;
}

.history-message p {
  margin: 0;
  color: inherit;
  line-height: 1.6;
}

/* Responsive tasarım */
@media (max-width: 768px) {
  .voice-chatbot-layout {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .left-panel {
    width: 280px;
    min-width: 200px;
    height: calc(100vh - 1rem);
    gap: 0.5rem;
  }

  .main-panel {
    height: calc(100vh - 1rem);
  }
}

@media (max-width: 480px) {
  .voice-chatbot-layout {
    padding: 0.25rem;
    gap: 0.25rem;
  }

  .left-panel {
    width: 240px;
    min-width: 180px;
    height: calc(100vh - 0.5rem);
    gap: 0.25rem;
  }

  .main-panel {
    height: calc(100vh - 0.5rem);
  }
}
