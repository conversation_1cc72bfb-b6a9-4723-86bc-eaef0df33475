'use client';

import { useEffect, useRef, useState } from 'react';

interface AuraCircleProps {
  isRecording: boolean;
  isProcessing: boolean;
  audioStream?: MediaStream | null;
}

const AuraCircle = ({ isRecording, isProcessing, audioStream }: AuraCircleProps) => {
  const [audioLevel, setAudioLevel] = useState(0);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number>();

  useEffect(() => {
    if (isRecording && audioStream) {
      // Audio context ve analyser oluştur
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const source = audioContext.createMediaStreamSource(audioStream);
      
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.8;
      source.connect(analyser);
      
      analyserRef.current = analyser;
      
      // Ses seviyesini sürekli analiz et
      const analyzeAudio = () => {
        if (analyserRef.current) {
          const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
          analyserRef.current.getByteFrequencyData(dataArray);
          
          // Ortalama ses seviyesini hesapla
          const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
          const normalizedLevel = Math.min(average / 128, 1); // 0-1 arası normalize et
          
          setAudioLevel(normalizedLevel);
        }
        
        if (isRecording) {
          animationFrameRef.current = requestAnimationFrame(analyzeAudio);
        }
      };
      
      analyzeAudio();
      
      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        audioContext.close();
      };
    } else {
      setAudioLevel(0);
    }
  }, [isRecording, audioStream]);

  // Aura circle boyutunu ses seviyesine göre hesapla
  const getCircleScale = () => {
    if (isProcessing) return 1.1;
    if (isRecording) return 1 + (audioLevel * 0.5); // Ses seviyesine göre büyüt
    return 1;
  };

  // Aura renk gradyanını ses seviyesine göre hesapla
  const getAuraColors = () => {
    if (isProcessing) {
      return {
        inner: 'rgba(251, 191, 36, 0.8)', // Amber
        middle: 'rgba(245, 158, 11, 0.6)',
        outer: 'rgba(217, 119, 6, 0.4)'
      };
    }
    
    if (isRecording) {
      const intensity = audioLevel;
      const baseBlue = 59 + (intensity * 40);
      const basePurple = 147 + (intensity * 30);
      const basePink = 236 + (intensity * 20);

      return {
        inner: `rgba(${baseBlue}, 130, 246, ${0.8 + intensity * 0.2})`,
        middle: `rgba(${basePurple}, 51, 234, ${0.6 + intensity * 0.3})`,
        outer: `rgba(${basePink}, 72, 153, ${0.4 + intensity * 0.4})`
      };
    }
    
    return {
      inner: 'rgba(156, 163, 175, 0.3)', // Gray
      middle: 'rgba(107, 114, 128, 0.2)',
      outer: 'rgba(75, 85, 99, 0.1)'
    };
  };

  const colors = getAuraColors();
  const scale = getCircleScale();

  return (
    <div className="flex items-center justify-center h-full">
      <div className="relative w-80 h-80 flex items-center justify-center">
        {/* Outer Aura */}
        <div
          className={`absolute rounded-full blur-xl transition-all duration-300 ease-out ${
            isRecording ? 'animate-pulse' : ''
          }`}
          style={{
            width: '300px',
            height: '300px',
            background: `radial-gradient(circle, ${colors.outer} 0%, transparent 70%)`,
            transform: `scale(${scale * 1.2})`,
            animationDuration: '2s',
            top: '50%',
            left: '50%',
            marginTop: '-150px',
            marginLeft: '-150px',
            zIndex: 1
          }}
        />

        {/* Middle Aura */}
        <div
          className={`absolute rounded-full blur-lg transition-all duration-200 ease-out ${
            isRecording ? 'animate-pulse' : ''
          }`}
          style={{
            width: '300px',
            height: '300px',
            background: `radial-gradient(circle, ${colors.middle} 0%, transparent 60%)`,
            transform: `scale(${scale * 1.1})`,
            animationDuration: '1.5s',
            animationDirection: 'reverse',
            top: '50%',
            left: '50%',
            marginTop: '-150px',
            marginLeft: '-150px',
            zIndex: 2
          }}
        />

        {/* Inner Aura */}
        <div
          className={`absolute rounded-full blur-md transition-all duration-100 ease-out ${
            isRecording ? 'animate-pulse' : ''
          }`}
          style={{
            width: '300px',
            height: '300px',
            background: `radial-gradient(circle, ${colors.inner} 0%, transparent 50%)`,
            transform: `scale(${scale})`,
            animationDuration: '1s',
            top: '50%',
            left: '50%',
            marginTop: '-150px',
            marginLeft: '-150px',
            zIndex: 3
          }}
        />

        {/* Core Circle */}
        <div
          className="absolute w-32 h-32 rounded-full flex items-center justify-center text-4xl transition-all duration-200 ease-out"
          style={{
            background: isRecording
              ? 'linear-gradient(135deg, #3B82F6, #8B5CF6, #EC4899)'
              : isProcessing
              ? 'linear-gradient(135deg, #F59E0B, #EF4444)'
              : 'linear-gradient(135deg, #6B7280, #4B5563)',
            transform: `scale(${scale})`,
            boxShadow: isRecording
              ? `0 0 ${20 + audioLevel * 30}px rgba(59, 130, 246, 0.5)`
              : isProcessing
              ? '0 0 25px rgba(251, 191, 36, 0.5)'
              : '0 0 10px rgba(107, 114, 128, 0.3)',
            top: '50%',
            left: '50%',
            marginTop: '-64px',
            marginLeft: '-64px',
            zIndex: 10 // Core circle en üstte
          }}
        >
          {isProcessing ? '⏳' : '🎤'}
        </div>
        
        {/* Ses seviyesi göstergesi */}
        {isRecording && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full mt-4">
            <div className="flex items-center space-x-1">
              {[...Array(10)].map((_, i) => (
                <div
                  key={i}
                  className={`w-1 h-4 rounded-full transition-all duration-100 ${
                    i < audioLevel * 10 
                      ? 'bg-blue-500' 
                      : 'bg-gray-300'
                  }`}
                  style={{
                    height: `${8 + i * 2}px`
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuraCircle;
