'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';

interface Conversation {
  id: number;
  session_id: string;
  title: string;
  last_message_at: string;
  created_at: string;
  message_count: number;
  last_message?: {
    type: 'user' | 'assistant';
    content: string;
  };
}

interface ConversationHistoryProps {
  currentSessionId?: string | null;
  onConversationSelect: (sessionId: string) => void;
  onNewConversation: () => void;
  refreshTrigger?: number; // Yeni sohbet oluşturulduğunda listeyi yenilemek için
}

const ConversationHistory = ({
  currentSessionId,
  onConversationSelect,
  onNewConversation,
  refreshTrigger
}: ConversationHistoryProps) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

  // Sohbet geçmişini yükle
  const loadConversations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`${API_BASE_URL}/conversations`);
      
      if (response.data.success) {
        setConversations(response.data.conversations);
      } else {
        setError('Sohbet geçmişi yüklenemedi');
      }
    } catch (err: any) {
      console.error('Failed to load conversations:', err);
      setError('Bağlantı hatası');
    } finally {
      setLoading(false);
    }
  };

  // Component mount olduğunda ve refreshTrigger değiştiğinde sohbet geçmişini yükle
  useEffect(() => {
    loadConversations();
  }, [refreshTrigger]);

  // Yeni sohbet oluştur
  const handleNewConversation = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/conversations`);
      
      if (response.data.success) {
        onNewConversation();
        loadConversations(); // Listeyi yenile
      }
    } catch (err) {
      console.error('Failed to create new conversation:', err);
    }
  };

  // Sohbet sil
  const deleteConversation = async (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Parent click event'ini engelle
    
    if (!confirm('Bu sohbeti silmek istediğinizden emin misiniz?')) {
      return;
    }

    try {
      await axios.delete(`${API_BASE_URL}/conversations/${sessionId}`);
      loadConversations(); // Listeyi yenile
      
      // Eğer silinen sohbet aktif sohbetse, yeni sohbet başlat
      if (sessionId === currentSessionId) {
        onNewConversation();
      }
    } catch (err) {
      console.error('Failed to delete conversation:', err);
      alert('Sohbet silinemedi');
    }
  };

  // Tarih formatla
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Az önce';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} saat önce`;
    } else if (diffInHours < 48) {
      return 'Dün';
    } else {
      return date.toLocaleDateString('tr-TR', {
        day: 'numeric',
        month: 'short'
      });
    }
  };

  return (
    <div className="h-full flex flex-col bg-black/20 backdrop-blur-sm rounded-2xl border border-white/10">
      {/* Header */}
      <div className="p-4 border-b border-white/10 flex-shrink-0">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-white">Sohbet Geçmişi</h3>
          <button
            onClick={handleNewConversation}
            className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
            title="Yeni Sohbet"
          >
            + Yeni
          </button>
        </div>

        {error && (
          <div className="text-red-400 text-xs bg-red-500/10 border border-red-500/20 rounded px-2 py-1">
            {error}
          </div>
        )}
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto min-h-0">
        {loading ? (
          <div className="p-4 text-center">
            <div className="text-gray-400 text-sm">Yükleniyor...</div>
          </div>
        ) : conversations.length === 0 ? (
          <div className="p-4 text-center">
            <div className="text-gray-400 text-sm mb-2">Henüz sohbet yok</div>
            <button
              onClick={handleNewConversation}
              className="text-blue-400 hover:text-blue-300 text-sm underline"
            >
              İlk sohbeti başlat
            </button>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {conversations.map((conversation) => (
              <div
                key={conversation.session_id}
                onClick={() => onConversationSelect(conversation.session_id)}
                className={`group p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                  conversation.session_id === currentSessionId
                    ? 'bg-blue-600/20 border border-blue-500/30'
                    : 'hover:bg-white/5 border border-transparent'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-white text-sm font-medium truncate mb-1">
                      {conversation.title || 'Yeni Sohbet'}
                    </h4>
                    
                    {conversation.last_message && (
                      <p className="text-gray-400 text-xs truncate mb-1">
                        {conversation.last_message.type === 'user' ? '👤' : '🤖'} {conversation.last_message.content}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500 text-xs">
                        {formatDate(conversation.last_message_at || conversation.created_at)}
                      </span>
                      <span className="text-gray-500 text-xs">
                        {conversation.message_count} mesaj
                      </span>
                    </div>
                  </div>
                  
                  <button
                    onClick={(e) => deleteConversation(conversation.session_id, e)}
                    className="opacity-0 group-hover:opacity-100 ml-2 p-1 text-gray-400 hover:text-red-400 transition-all"
                    title="Sohbeti sil"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-white/10 flex-shrink-0">
        <div className="text-xs text-gray-500 text-center">
          {conversations.length} sohbet
        </div>
      </div>
    </div>
  );
};

export default ConversationHistory;
