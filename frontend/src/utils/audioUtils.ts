/**
 * Safari ve diğer tarayıcılar için ses kayıt uyumluluğu
 */

export interface AudioRecordingOptions {
  echoCancellation?: boolean;
  noiseSuppression?: boolean;
  autoGainControl?: boolean;
}

export interface RecordingResult {
  isSupported: boolean;
  mediaRecorder?: MediaRecorder;
  stream?: MediaStream;
  error?: string;
}

/**
 * <PERSON>yıcının ses kaydını destekleyip desteklemediğini kontrol et
 */
export function isAudioRecordingSupported(): boolean {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia && window.MediaRecorder);
}

/**
 * Safari için uyumlu MIME type'ları
 */
export function getSupportedMimeTypes(): string[] {
  const types = [
    'audio/webm;codecs=opus',
    'audio/webm',
    'audio/mp4',
    'audio/mp4;codecs=mp4a.40.2',
    'audio/mpeg',
    'audio/wav',
  ];

  return types.filter(type => MediaRecorder.isTypeSupported(type));
}

/**
 * En uygun MIME type'ı seç
 */
export function getBestMimeType(): string {
  const supportedTypes = getSupportedMimeTypes();
  
  // Öncelik sırası: WebM > MP4 > WAV
  const preferredOrder = [
    'audio/webm;codecs=opus',
    'audio/webm',
    'audio/mp4;codecs=mp4a.40.2',
    'audio/mp4',
    'audio/wav'
  ];

  for (const type of preferredOrder) {
    if (supportedTypes.includes(type)) {
      return type;
    }
  }

  return supportedTypes[0] || 'audio/webm';
}

/**
 * Safari uyumlu ses kaydı başlat
 */
export async function startAudioRecording(options: AudioRecordingOptions = {}): Promise<RecordingResult> {
  try {
    // Tarayıcı desteği kontrolü
    if (!isAudioRecordingSupported()) {
      return {
        isSupported: false,
        error: 'Tarayıcınız ses kaydını desteklemiyor'
      };
    }

    // Mikrofon erişimi
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: options.echoCancellation ?? true,
        noiseSuppression: options.noiseSuppression ?? true,
        autoGainControl: options.autoGainControl ?? true,
        // Safari için ek ayarlar
        sampleRate: 44100,
        channelCount: 1,
      }
    });

    // En uygun MIME type'ı seç
    const mimeType = getBestMimeType();
    console.log('Using MIME type:', mimeType);

    // MediaRecorder oluştur
    const mediaRecorder = new MediaRecorder(stream, {
      mimeType,
      // Safari için düşük bitrate
      audioBitsPerSecond: 128000,
    });

    return {
      isSupported: true,
      mediaRecorder,
      stream,
    };

  } catch (error: any) {
    console.error('Audio recording setup failed:', error);
    
    let errorMessage = 'Ses kaydı başlatılamadı';
    
    if (error.name === 'NotAllowedError') {
      errorMessage = 'Mikrofon erişimi reddedildi. Tarayıcı ayarlarından mikrofon iznini kontrol edin.';
    } else if (error.name === 'NotFoundError') {
      errorMessage = 'Mikrofon bulunamadı. Mikrofonunuzun bağlı olduğundan emin olun.';
    } else if (error.name === 'NotSupportedError') {
      errorMessage = 'Tarayıcınız ses kaydını desteklemiyor.';
    } else if (error.name === 'OverconstrainedError') {
      errorMessage = 'Mikrofon ayarları desteklenmiyor. Farklı bir mikrofon deneyin.';
    }

    return {
      isSupported: false,
      error: errorMessage
    };
  }
}

/**
 * Ses kaydını durdur ve temizle
 */
export function stopAudioRecording(mediaRecorder?: MediaRecorder, stream?: MediaStream): void {
  try {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
      mediaRecorder.stop();
    }
    
    if (stream) {
      stream.getTracks().forEach(track => {
        track.stop();
      });
    }
  } catch (error) {
    console.error('Error stopping audio recording:', error);
  }
}

/**
 * Tarayıcı bilgilerini al
 */
export function getBrowserInfo(): { name: string; version: string; isSafari: boolean; isChrome: boolean; isFirefox: boolean } {
  const userAgent = navigator.userAgent;
  
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
  const isChrome = /Chrome/.test(userAgent) && !/Edge/.test(userAgent);
  const isFirefox = /Firefox/.test(userAgent);
  
  let name = 'Unknown';
  let version = 'Unknown';
  
  if (isSafari) {
    name = 'Safari';
    const match = userAgent.match(/Version\/([0-9.]+)/);
    version = match ? match[1] : 'Unknown';
  } else if (isChrome) {
    name = 'Chrome';
    const match = userAgent.match(/Chrome\/([0-9.]+)/);
    version = match ? match[1] : 'Unknown';
  } else if (isFirefox) {
    name = 'Firefox';
    const match = userAgent.match(/Firefox\/([0-9.]+)/);
    version = match ? match[1] : 'Unknown';
  }
  
  return { name, version, isSafari, isChrome, isFirefox };
}
