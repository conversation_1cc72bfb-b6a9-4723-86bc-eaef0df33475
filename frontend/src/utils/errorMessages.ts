export const getErrorMessage = (error: any): string => {
  // Network errors
  if (error.code === 'ECONNREFUSED' || error.message?.includes('Network Error')) {
    return 'Backend sunucusuna bağlanılamıyor. Sunucunun çalıştığından emin olun.';
  }

  // HTTP status errors
  if (error.response?.status) {
    switch (error.response.status) {
      case 400:
        return 'Geçersiz istek. Lütfen ses dosyanızı kontrol edin.';
      case 413:
        return 'Ses dosyası çok büyük. Maksimum 10MB olmalıdır.';
      case 422:
        return 'Desteklenmeyen ses formatı. WAV, MP3, M4A veya WebM kullanın.';
      case 429:
        return 'Çok fazla istek gönderdiniz. Lütfen biraz bekleyin.';
      case 500:
        return 'Sunucu hatası. API anahtarlarının doğru yapılandırıldığından emin olun.';
      case 503:
        return 'Servis geçici olarak kullanılamıyor. Lütfen daha sonra tekrar deneyin.';
      default:
        return `HTTP ${error.response.status} hatası oluştu.`;
    }
  }

  // API specific errors
  if (error.response?.data?.error) {
    return error.response.data.error;
  }

  // Timeout errors
  if (error.code === 'ECONNABORTED') {
    return 'İstek zaman aşımına uğradı. Lütfen tekrar deneyin.';
  }

  // Generic error
  return error.message || 'Beklenmeyen bir hata oluştu.';
};

export const getMicrophoneErrorMessage = (error: any): string => {
  if (error.name === 'NotAllowedError') {
    return 'Mikrofon erişimi reddedildi. Tarayıcı ayarlarından mikrofon iznini kontrol edin.';
  }
  
  if (error.name === 'NotFoundError') {
    return 'Mikrofon bulunamadı. Mikrofonunuzun bağlı olduğundan emin olun.';
  }
  
  if (error.name === 'NotSupportedError') {
    return 'Tarayıcınız ses kaydını desteklemiyor.';
  }
  
  return 'Mikrofon erişimi sağlanamadı. Lütfen tarayıcı ayarlarını kontrol edin.';
};
