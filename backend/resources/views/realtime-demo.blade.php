<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>OpenAI Realtime API Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .status {
            padding: 15px 30px;
            text-align: center;
            font-weight: 500;
            border-bottom: 1px solid #eee;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
        }

        .controls {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            border-bottom: 1px solid #eee;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 65, 108, 0.4);
        }

        .btn-record {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: #333;
        }

        .btn-record:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 154, 158, 0.4);
        }

        .btn-record.recording {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .text-input-group {
            grid-column: 1 / -1;
            display: flex;
            gap: 10px;
        }

        input[type="text"] {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px 30px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
        }

        .footer {
            padding: 20px 30px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
            background: #f8f9fa;
        }

        .recording-indicator {
            display: none;
            align-items: center;
            gap: 10px;
            color: #dc3545;
            font-weight: 500;
        }

        .recording-indicator.active {
            display: flex;
        }

        .recording-dot {
            width: 10px;
            height: 10px;
            background: #dc3545;
            border-radius: 50%;
            animation: pulse 1s infinite;
        }

        @media (max-width: 768px) {
            .controls {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container" id="realtime-demo">
        <div class="header">
            <h1>🤖 OpenAI Realtime API</h1>
            <p>WebSocket ile gerçek zamanlı AI konuşması</p>
        </div>

        <div id="status" class="status">
            Bağlantı bekleniyor...
        </div>

        <div class="controls">
            <div class="control-group">
                <label>Bağlantı Kontrolü</label>
                <button id="connect-btn" class="btn-success">🔗 Bağlan</button>
                <button id="disconnect-btn" class="btn-danger">🔌 Bağlantıyı Kes</button>
            </div>

            <div class="control-group">
                <label>Ses Kaydı</label>
                <button id="record-btn" class="btn-record">🎤 Kayıt Başlat</button>
                <div class="recording-indicator" id="recording-indicator">
                    <div class="recording-dot"></div>
                    <span>Kayıt yapılıyor...</span>
                </div>
            </div>

            <div class="text-input-group">
                <input type="text" id="text-input" placeholder="Mesajınızı yazın..." maxlength="1000">
                <button id="send-text-btn" class="btn-primary">📤 Gönder</button>
            </div>
        </div>

        <div id="messages" class="messages">
            <div class="message assistant">
                Merhaba! OpenAI Realtime API demo'suna hoş geldiniz. Bağlantı kurmak için yukarıdaki "Bağlan" butonuna tıklayın.
            </div>
        </div>

        <div class="footer">
            <p>🚀 OpenAI Realtime API ile güçlendirilmiştir | WebSocket bağlantısı üzerinden gerçek zamanlı iletişim</p>
        </div>
    </div>

    <script src="{{ asset('js/realtime-client.js') }}"></script>
    <script>
        // Sayfa yüklendiğinde otomatik başlatma
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 OpenAI Realtime Demo başlatılıyor...');
            
            // Enter tuşu ile mesaj gönderme
            document.getElementById('text-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('send-text-btn').click();
                }
            });

            // Kayıt butonu durumunu güncelle
            const recordBtn = document.getElementById('record-btn');
            const recordingIndicator = document.getElementById('recording-indicator');
            
            recordBtn.addEventListener('click', function() {
                if (this.classList.contains('recording')) {
                    this.classList.remove('recording');
                    this.textContent = '🎤 Kayıt Başlat';
                    recordingIndicator.classList.remove('active');
                } else {
                    this.classList.add('recording');
                    this.textContent = '⏹️ Kaydı Durdur';
                    recordingIndicator.classList.add('active');
                }
            });

            // Otomatik durum kontrolü
            setInterval(async function() {
                if (window.realtimeClient) {
                    try {
                        await window.realtimeClient.checkStatus();
                    } catch (error) {
                        console.log('Durum kontrolü başarısız:', error.message);
                    }
                }
            }, 30000); // 30 saniyede bir kontrol et
        });
    </script>
</body>
</html>
