<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>OpenAI Streaming Chat Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: grid;
            grid-template-rows: auto 1fr auto;
            height: 90vh;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .status-bar {
            padding: 15px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status {
            font-weight: 500;
        }

        .status.success { color: #28a745; }
        .status.error { color: #dc3545; }
        .status.warning { color: #ffc107; }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px 30px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
        }

        .message.streaming .message-content {
            position: relative;
        }

        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4facfe;
            animation: typing 1.4s infinite ease-in-out;
            margin-left: 5px;
        }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .input-area {
            padding: 20px 30px;
            background: white;
            border-top: 1px solid #eee;
        }

        .input-group {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        textarea {
            width: 100%;
            min-height: 50px;
            max-height: 120px;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 1em;
            font-family: inherit;
            resize: none;
            transition: border-color 0.3s ease;
        }

        textarea:focus {
            outline: none;
            border-color: #4facfe;
        }

        .send-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .send-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .control-btn {
            padding: 8px 16px;
            border: 1px solid #e1e5e9;
            background: white;
            border-radius: 20px;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #f8f9fa;
        }

        .control-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
                height: 95vh;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 OpenAI Streaming Chat</h1>
            <p>Gerçek zamanlı AI sohbeti - HTTP Streaming & WebSocket desteği</p>
        </div>

        <div class="status-bar">
            <div id="connection-status" class="status">Bağlantı kontrol ediliyor...</div>
            <div class="controls">
                <button class="control-btn active" data-mode="streaming">📡 Streaming</button>
                <button class="control-btn" data-mode="normal">💬 Normal</button>
                <button class="control-btn" data-mode="websocket">🔌 WebSocket</button>
            </div>
        </div>

        <div class="chat-container">
            <div id="messages" class="messages">
                <div class="message assistant">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        Merhaba! Ben OpenAI destekli asistanınızım. Size nasıl yardımcı olabilirim?
                        <br><br>
                        <strong>Özellikler:</strong><br>
                        📡 <strong>Streaming:</strong> Gerçek zamanlı yanıt akışı<br>
                        💬 <strong>Normal:</strong> Geleneksel sohbet<br>
                        🔌 <strong>WebSocket:</strong> Gerçek zamanlı bağlantı (hazırlanıyor)
                    </div>
                </div>
            </div>

            <div class="input-area">
                <div class="input-group">
                    <div class="input-wrapper">
                        <textarea 
                            id="message-input" 
                            placeholder="Mesajınızı yazın..." 
                            maxlength="1000"
                            rows="1"
                        ></textarea>
                    </div>
                    <button id="send-btn" class="send-btn">Gönder</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class StreamingChat {
            constructor() {
                this.mode = 'streaming';
                this.isStreaming = false;
                this.currentEventSource = null;
                
                this.initElements();
                this.bindEvents();
                this.checkConnectionStatus();
            }

            initElements() {
                this.messagesContainer = document.getElementById('messages');
                this.messageInput = document.getElementById('message-input');
                this.sendBtn = document.getElementById('send-btn');
                this.statusEl = document.getElementById('connection-status');
                this.controlBtns = document.querySelectorAll('.control-btn');
            }

            bindEvents() {
                this.sendBtn.addEventListener('click', () => this.sendMessage());
                
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                this.messageInput.addEventListener('input', () => {
                    this.autoResize();
                });

                this.controlBtns.forEach(btn => {
                    btn.addEventListener('click', () => {
                        this.setMode(btn.dataset.mode);
                    });
                });
            }

            setMode(mode) {
                this.mode = mode;
                this.controlBtns.forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.mode === mode);
                });
                
                this.updateStatus(`Mod: ${mode.charAt(0).toUpperCase() + mode.slice(1)}`);
            }

            autoResize() {
                const textarea = this.messageInput;
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
            }

            async checkConnectionStatus() {
                try {
                    const response = await fetch('/api/streaming/websocket-status');
                    const data = await response.json();
                    
                    if (data.websocket_available) {
                        this.updateStatus('WebSocket desteği mevcut', 'success');
                    } else {
                        this.updateStatus('WebSocket desteği yok - HTTP Streaming aktif', 'warning');
                    }
                } catch (error) {
                    this.updateStatus('Bağlantı kontrolü başarısız', 'error');
                }
            }

            updateStatus(message, type = '') {
                this.statusEl.textContent = message;
                this.statusEl.className = `status ${type}`;
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isStreaming) return;

                this.addMessage('user', message);
                this.messageInput.value = '';
                this.autoResize();

                if (this.mode === 'streaming') {
                    await this.sendStreamingMessage(message);
                } else if (this.mode === 'websocket') {
                    await this.sendWebSocketMessage(message);
                } else {
                    await this.sendNormalMessage(message);
                }
            }

            async sendStreamingMessage(message) {
                this.isStreaming = true;
                this.sendBtn.disabled = true;
                this.updateStatus('Streaming yanıt alınıyor...', 'warning');

                const assistantMessage = this.addMessage('assistant', '', true);
                
                try {
                    const response = await fetch('/api/streaming/stream-chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({ message })
                    });

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop();

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    if (data.content) {
                                        this.appendToMessage(assistantMessage, data.content);
                                    }
                                } catch (e) {
                                    console.log('JSON parse error:', e);
                                }
                            }
                        }
                    }

                    this.finalizeMessage(assistantMessage);
                    this.updateStatus('Streaming tamamlandı', 'success');

                } catch (error) {
                    this.updateMessage(assistantMessage, 'Üzgünüm, bir hata oluştu: ' + error.message);
                    this.updateStatus('Streaming hatası', 'error');
                }

                this.isStreaming = false;
                this.sendBtn.disabled = false;
            }

            async sendNormalMessage(message) {
                this.sendBtn.disabled = true;
                this.updateStatus('Yanıt bekleniyor...', 'warning');

                const assistantMessage = this.addMessage('assistant', 'Düşünüyor...', true);

                try {
                    const response = await fetch('/api/streaming/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({ message })
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.updateMessage(assistantMessage, data.response);
                        this.updateStatus('Yanıt alındı', 'success');
                    } else {
                        this.updateMessage(assistantMessage, 'Hata: ' + data.error);
                        this.updateStatus('Hata oluştu', 'error');
                    }

                } catch (error) {
                    this.updateMessage(assistantMessage, 'Bağlantı hatası: ' + error.message);
                    this.updateStatus('Bağlantı hatası', 'error');
                }

                this.sendBtn.disabled = false;
            }

            async sendWebSocketMessage(message) {
                this.updateStatus('WebSocket henüz hazır değil', 'warning');
                // WebSocket implementation will be added here
            }

            addMessage(sender, content, isStreaming = false) {
                const messageEl = document.createElement('div');
                messageEl.className = `message ${sender}${isStreaming ? ' streaming' : ''}`;
                
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = sender === 'user' ? '👤' : '🤖';
                
                const contentEl = document.createElement('div');
                contentEl.className = 'message-content';
                contentEl.textContent = content;
                
                if (isStreaming) {
                    const indicator = document.createElement('span');
                    indicator.className = 'typing-indicator';
                    contentEl.appendChild(indicator);
                }
                
                messageEl.appendChild(avatar);
                messageEl.appendChild(contentEl);
                
                this.messagesContainer.appendChild(messageEl);
                this.scrollToBottom();
                
                return messageEl;
            }

            appendToMessage(messageEl, content) {
                const contentEl = messageEl.querySelector('.message-content');
                const indicator = contentEl.querySelector('.typing-indicator');
                
                if (indicator) {
                    contentEl.insertBefore(document.createTextNode(content), indicator);
                } else {
                    contentEl.textContent += content;
                }
                
                this.scrollToBottom();
            }

            updateMessage(messageEl, content) {
                const contentEl = messageEl.querySelector('.message-content');
                contentEl.textContent = content;
                this.scrollToBottom();
            }

            finalizeMessage(messageEl) {
                messageEl.classList.remove('streaming');
                const indicator = messageEl.querySelector('.typing-indicator');
                if (indicator) {
                    indicator.remove();
                }
            }

            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
        }

        // Initialize chat when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new StreamingChat();
        });
    </script>
</body>
</html>
