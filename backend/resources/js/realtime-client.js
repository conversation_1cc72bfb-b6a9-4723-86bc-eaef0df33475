/**
 * OpenAI Realtime API Frontend Client
 * Bu dosya frontend'de OpenAI Realtime API ile WebSocket bağlantısı kurmak için kullanılır
 */

class OpenAIRealtimeClient {
    constructor(apiBaseUrl = '/api/realtime') {
        this.apiBaseUrl = apiBaseUrl;
        this.isConnected = false;
        this.eventHandlers = {};
        this.audioContext = null;
        this.mediaRecorder = null;
        this.audioChunks = [];
    }

    /**
     * Event handler ekle
     */
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
        return this;
    }

    /**
     * Event tetikle
     */
    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => handler(data));
        }
    }

    /**
     * OpenAI Realtime API'ye bağlan
     */
    async connect() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/connect`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.isConnected = true;
                this.emit('connect', result);
                console.log('✅ OpenAI Realtime API bağlantısı kuruldu');
            } else {
                throw new Error(result.message || 'Bağlantı kurulamadı');
            }
        } catch (error) {
            console.error('❌ Bağlantı hatası:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Metin mesajı gönder
     */
    async sendText(message) {
        if (!this.isConnected) {
            throw new Error('Bağlantı kurulmamış');
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/send-text`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({ message })
            });

            const result = await response.json();
            
            if (result.success) {
                this.emit('textSent', { message, result });
                console.log('📤 Metin mesajı gönderildi:', message);
            } else {
                throw new Error(result.message || 'Mesaj gönderilemedi');
            }
        } catch (error) {
            console.error('❌ Metin gönderme hatası:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Ses kaydını başlat
     */
    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    sampleRate: 24000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                } 
            });

            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 24000
            });

            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = async () => {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
                await this.processAudioBlob(audioBlob);
            };

            this.mediaRecorder.start(100); // 100ms chunks
            this.emit('recordingStarted');
            console.log('🎤 Ses kaydı başlatıldı');

        } catch (error) {
            console.error('❌ Ses kaydı başlatma hatası:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Ses kaydını durdur
     */
    stopRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();
            this.emit('recordingStopped');
            console.log('⏹️ Ses kaydı durduruldu');
        }
    }

    /**
     * Ses blob'unu işle ve gönder
     */
    async processAudioBlob(audioBlob) {
        try {
            // AudioBlob'u PCM16'ya dönüştür
            const arrayBuffer = await audioBlob.arrayBuffer();
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            
            // PCM16 formatına dönüştür
            const pcm16Data = this.convertToPCM16(audioBuffer);
            const base64Audio = btoa(String.fromCharCode(...pcm16Data));
            
            await this.sendAudio(base64Audio);
            
        } catch (error) {
            console.error('❌ Ses işleme hatası:', error);
            this.emit('error', error);
        }
    }

    /**
     * AudioBuffer'ı PCM16'ya dönüştür
     */
    convertToPCM16(audioBuffer) {
        const samples = audioBuffer.getChannelData(0);
        const pcm16 = new Int16Array(samples.length);
        
        for (let i = 0; i < samples.length; i++) {
            const sample = Math.max(-1, Math.min(1, samples[i]));
            pcm16[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        }
        
        return new Uint8Array(pcm16.buffer);
    }

    /**
     * Ses verisi gönder
     */
    async sendAudio(audioBase64) {
        if (!this.isConnected) {
            throw new Error('Bağlantı kurulmamış');
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/send-audio`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({ audio: audioBase64 })
            });

            const result = await response.json();
            
            if (result.success) {
                this.emit('audioSent', result);
                console.log('🔊 Ses verisi gönderildi');
            } else {
                throw new Error(result.message || 'Ses gönderilemedi');
            }
        } catch (error) {
            console.error('❌ Ses gönderme hatası:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Bağlantı durumunu kontrol et
     */
    async checkStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/status`);
            const result = await response.json();
            
            this.isConnected = result.connected;
            this.emit('statusUpdate', result);
            
            return result;
        } catch (error) {
            console.error('❌ Durum kontrol hatası:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Bağlantıyı kes
     */
    async disconnect() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/disconnect`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.isConnected = false;
                this.emit('disconnect', result);
                console.log('🔌 Bağlantı kesildi');
            }
        } catch (error) {
            console.error('❌ Bağlantı kesme hatası:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Cleanup
     */
    cleanup() {
        if (this.mediaRecorder) {
            this.mediaRecorder.stop();
        }
        if (this.audioContext) {
            this.audioContext.close();
        }
        this.audioChunks = [];
    }
}

// Global olarak kullanılabilir hale getir
window.OpenAIRealtimeClient = OpenAIRealtimeClient;

// Örnek kullanım
document.addEventListener('DOMContentLoaded', function() {
    // Eğer realtime sayfasındaysak client'ı başlat
    if (document.getElementById('realtime-demo')) {
        initRealtimeDemo();
    }
});

function initRealtimeDemo() {
    const client = new OpenAIRealtimeClient();
    
    // Event handler'ları ayarla
    client
        .on('connect', () => {
            updateStatus('Bağlandı', 'success');
        })
        .on('textSent', (data) => {
            addMessage('user', data.message);
        })
        .on('audioSent', () => {
            addMessage('user', '[Ses mesajı gönderildi]');
        })
        .on('error', (error) => {
            updateStatus('Hata: ' + error.message, 'error');
        })
        .on('disconnect', () => {
            updateStatus('Bağlantı kesildi', 'warning');
        });

    // UI event'leri
    document.getElementById('connect-btn')?.addEventListener('click', () => {
        client.connect();
    });

    document.getElementById('disconnect-btn')?.addEventListener('click', () => {
        client.disconnect();
    });

    document.getElementById('send-text-btn')?.addEventListener('click', () => {
        const input = document.getElementById('text-input');
        if (input.value.trim()) {
            client.sendText(input.value.trim());
            input.value = '';
        }
    });

    document.getElementById('record-btn')?.addEventListener('click', () => {
        if (client.mediaRecorder?.state === 'recording') {
            client.stopRecording();
        } else {
            client.startRecording();
        }
    });
}

function updateStatus(message, type) {
    const statusEl = document.getElementById('status');
    if (statusEl) {
        statusEl.textContent = message;
        statusEl.className = `status ${type}`;
    }
}

function addMessage(sender, message) {
    const messagesEl = document.getElementById('messages');
    if (messagesEl) {
        const messageEl = document.createElement('div');
        messageEl.className = `message ${sender}`;
        messageEl.textContent = `${sender}: ${message}`;
        messagesEl.appendChild(messageEl);
        messagesEl.scrollTop = messagesEl.scrollHeight;
    }
}
