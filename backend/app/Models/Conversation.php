<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Conversation extends Model
{
    protected $fillable = [
        'title',
        'session_id',
        'last_message_at',
    ];

    protected $casts = [
        'last_message_at' => 'datetime',
    ];

    /**
     * Sohbete ait mesajlar
     */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class)->orderBy('created_at');
    }

    /**
     * Son mesaj
     */
    public function lastMessage()
    {
        return $this->hasOne(Message::class)->latestOfMany();
    }

    /**
     * Sohbet başlığını otomatik oluştur
     */
    public function generateTitle(): void
    {
        if (!$this->title) {
            $firstUserMessage = $this->messages()
                ->where('type', 'user')
                ->first();

            if ($firstUserMessage) {
                // İlk 50 karakteri al ve başlık yap
                $title = substr($firstUserMessage->content, 0, 50);
                if (strlen($firstUserMessage->content) > 50) {
                    $title .= '...';
                }
                $this->update(['title' => $title]);
            }
        }
    }
}
