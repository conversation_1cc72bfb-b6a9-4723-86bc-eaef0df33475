<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class WhisperService
{
    protected $client;
    protected $apiKey;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiKey = env('OPENAI_API_KEY');
    }

    /**
     * Transcribe audio using OpenAI Whisper API
     */
    public function transcribe(string $audioFilePath): string
    {
        try {
            if (!file_exists($audioFilePath)) {
                throw new \Exception("Audio file not found: {$audioFilePath}");
            }

            $response = $this->client->post('https://api.openai.com/v1/audio/transcriptions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                ],
                'multipart' => [
                    [
                        'name' => 'file',
                        'contents' => fopen($audioFilePath, 'r'),
                        'filename' => basename($audioFilePath),
                    ],
                    [
                        'name' => 'model',
                        'contents' => 'whisper-1',
                    ],
                    [
                        'name' => 'language',
                        'contents' => 'tr', // Turkish language
                    ],
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            
            if (!isset($data['text'])) {
                throw new \Exception('Invalid response from Whisper API');
            }

            return $data['text'];

        } catch (RequestException $e) {
            Log::error('Whisper API request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('Speech-to-text conversion failed');
        } catch (\Exception $e) {
            Log::error('Whisper service error', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
