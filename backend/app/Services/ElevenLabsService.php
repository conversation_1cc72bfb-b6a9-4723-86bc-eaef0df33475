<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class ElevenLabsService
{
    protected $client;
    protected $apiKey;
    protected $voiceId;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiKey = env('ELEVENLABS_API_KEY');
        $this->voiceId = env('ELEVENLABS_VOICE_ID', 'pNInz6obpgDQGcFmaJgB'); // Default voice ID
    }

    /**
     * Convert text to speech using ElevenLabs API
     */
    public function textToSpeech(string $text): string
    {
        try {
            $response = $this->client->post("https://api.elevenlabs.io/v1/text-to-speech/{$this->voiceId}", [
                'headers' => [
                    'Accept' => 'audio/mpeg',
                    'Content-Type' => 'application/json',
                    'xi-api-key' => $this->apiKey,
                ],
                'json' => [
                    'text' => $text,
                    'model_id' => 'eleven_multilingual_v2',
                    'voice_settings' => [
                        'stability' => 0.5,
                        'similarity_boost' => 0.5,
                        'style' => 0.0,
                        'use_speaker_boost' => true,
                    ],
                ],
            ]);

            return $response->getBody()->getContents();

        } catch (RequestException $e) {
            Log::error('ElevenLabs API request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('Text-to-speech conversion failed');
        } catch (\Exception $e) {
            Log::error('ElevenLabs service error', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Get available voices
     */
    public function getVoices(): array
    {
        try {
            $response = $this->client->get('https://api.elevenlabs.io/v1/voices', [
                'headers' => [
                    'Accept' => 'application/json',
                    'xi-api-key' => $this->apiKey,
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            return $data['voices'] ?? [];

        } catch (RequestException $e) {
            Log::error('ElevenLabs voices request failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            throw new \Exception('Failed to get voices');
        }
    }
}
