<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ConversationResource extends JsonResource
{
    /**
     * Dönüştürülecek kaynağı diziye çevirin.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'session_id' => $this->session_id,
            'title' => $this->title ?: 'Yeni Sohbet',
            'last_message_at' => $this->last_message_at,
            'created_at' => $this->created_at,
            'message_count' => $this->messages ? $this->messages->count() : 0,
            'last_message' => $this->lastMessage ? [
                'type' => $this->lastMessage->type,
                'content' => $this->lastMessage->content ? mb_substr($this->lastMessage->content, 0, 100, 'UTF-8') : null,
            ] : null,
        ];
    }
}

