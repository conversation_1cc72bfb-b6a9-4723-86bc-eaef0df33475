<?php

namespace App\Http\Controllers;

use App\Services\OpenAIRealtimeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class RealtimeController extends Controller
{
    protected $realtimeService;

    public function __construct()
    {
        $this->realtimeService = new OpenAIRealtimeService();
    }

    /**
     * Initialize WebSocket connection to OpenAI Realtime API
     */
    public function connect(Request $request): JsonResponse
    {
        try {
            // Set up event handlers
            $this->realtimeService
                ->onConnect(function () {
                    Log::info('Realtime connection established');
                })
                ->onMessage(function ($message) {
                    Log::info('Received realtime message', ['type' => $message['type'] ?? 'unknown']);
                    // Here you could broadcast to frontend via WebSocket/SSE
                })
                ->onError(function ($error) {
                    Log::error('Realtime connection error', ['error' => $error->getMessage()]);
                })
                ->onDisconnect(function () {
                    Log::info('Realtime connection disconnected');
                });

            // Connect to OpenAI
            $this->realtimeService->connect()
                ->then(function () {
                    Log::info('Successfully connected to OpenAI Realtime API');
                })
                ->otherwise(function ($error) {
                    Log::error('Failed to connect to OpenAI Realtime API', [
                        'error' => $error->getMessage()
                    ]);
                });

            // Start the event loop in a separate process or use async handling
            // For demonstration, we'll return success immediately
            return response()->json([
                'success' => true,
                'message' => 'Realtime connection initiated'
            ]);

        } catch (\Exception $e) {
            Log::error('Error initiating realtime connection', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate realtime connection',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send text message via realtime connection
     */
    public function sendText(Request $request): JsonResponse
    {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        try {
            if (!$this->realtimeService->isConnected()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Not connected to realtime API'
                ], 400);
            }

            $success = $this->realtimeService->sendTextMessage($request->input('message'));

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Text message sent successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send text message'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Error sending realtime text message', [
                'error' => $e->getMessage(),
                'message' => $request->input('message')
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send text message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send audio data via realtime connection
     */
    public function sendAudio(Request $request): JsonResponse
    {
        $request->validate([
            'audio' => 'required|string' // Base64 encoded audio data
        ]);

        try {
            if (!$this->realtimeService->isConnected()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Not connected to realtime API'
                ], 400);
            }

            $audioData = $request->input('audio');
            
            // Send audio data
            $success = $this->realtimeService->sendAudioData($audioData);

            if ($success) {
                // Commit the audio buffer to trigger response
                $commitSuccess = $this->realtimeService->commitAudioBuffer();
                
                return response()->json([
                    'success' => $commitSuccess,
                    'message' => $commitSuccess ? 'Audio sent and committed successfully' : 'Audio sent but failed to commit'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send audio data'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Error sending realtime audio', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send audio data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get connection status
     */
    public function status(): JsonResponse
    {
        return response()->json([
            'connected' => $this->realtimeService->isConnected(),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Disconnect from realtime API
     */
    public function disconnect(): JsonResponse
    {
        try {
            $this->realtimeService->disconnect();
            
            return response()->json([
                'success' => true,
                'message' => 'Disconnected from realtime API'
            ]);

        } catch (\Exception $e) {
            Log::error('Error disconnecting from realtime API', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to disconnect',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
