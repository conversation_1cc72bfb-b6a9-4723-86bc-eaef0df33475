<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\OpenAIService;
use App\Services\ElevenLabsService;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ChatController extends Controller
{
    protected $openAIService;
    protected $elevenLabsService;

    public function __construct(OpenAIService $openAIService, ElevenLabsService $elevenLabsService)
    {
        $this->openAIService = $openAIService;
        $this->elevenLabsService = $elevenLabsService;
    }

    /**
     * Send text message (with optional image) to ChatGPT
     */
    public function sendMessage(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'message' => 'nullable|string|max:2000',
                'image' => 'nullable|file|max:10240', // 10MB max - MIME type kontrolünü manuel ya<PERSON>z
                'session_id' => 'nullable|string',
            ]);

            $message = $request->input('message', '');
            $imageFile = $request->file('image');
            $sessionId = $request->input('session_id');

            // Debug için request bilgilerini logla
            Log::info('Chat request received', [
                'has_message' => !empty($message),
                'has_file' => $request->hasFile('image'),
                'file_info' => $imageFile ? [
                    'name' => $imageFile->getClientOriginalName(),
                    'size' => $imageFile->getSize(),
                    'mime' => $imageFile->getMimeType(),
                    'extension' => $imageFile->getClientOriginalExtension(),
                    'is_valid' => $imageFile->isValid()
                ] : null,
                'all_files' => $request->allFiles()
            ]);

            // En az bir içerik olmalı
            if (empty($message) && !$imageFile) {
                throw new \Exception('Mesaj veya görsel gönderilmelidir');
            }

            // Görsel varsa kaydet
            $imageUrl = null;
            if ($imageFile) {
                try {
                    // Dosya geçerliliğini kontrol et
                    if (!$imageFile->isValid()) {
                        throw new \Exception('Geçersiz görsel dosyası');
                    }

                    // Dosya boyutunu kontrol et
                    if ($imageFile->getSize() > 10 * 1024 * 1024) { // 10MB
                        throw new \Exception('Görsel dosyası çok büyük (maksimum 10MB)');
                    }

                    // MIME type kontrolü - octet-stream'i de kabul et
                    $mimeType = $imageFile->getMimeType();
                    $extension = strtolower($imageFile->getClientOriginalExtension());

                    $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/octet-stream'];
                    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                    // MIME type veya extension kontrolü
                    if (!in_array($mimeType, $allowedMimes) && !in_array($extension, $allowedExtensions)) {
                        throw new \Exception("Desteklenmeyen görsel formatı. MIME: {$mimeType}, Extension: {$extension}");
                    }

                    // Eğer octet-stream ise, extension'a göre MIME type'ı düzelt
                    if ($mimeType === 'application/octet-stream' && in_array($extension, $allowedExtensions)) {
                        $mimeTypeMap = [
                            'jpg' => 'image/jpeg',
                            'jpeg' => 'image/jpeg',
                            'png' => 'image/png',
                            'gif' => 'image/gif',
                            'webp' => 'image/webp'
                        ];
                        $mimeType = $mimeTypeMap[$extension] ?? 'image/jpeg';
                    }

                    $imagePath = $imageFile->store('images/uploads', 'public');
                    $imageUrl = Storage::disk('public')->url($imagePath);

                    Log::info('Image uploaded successfully', [
                        'path' => $imagePath,
                        'url' => $imageUrl,
                        'size' => $imageFile->getSize(),
                        'mime' => $imageFile->getMimeType()
                    ]);
                } catch (\Exception $e) {
                    Log::error('Image upload failed', [
                        'error' => $e->getMessage(),
                        'file_size' => $imageFile ? $imageFile->getSize() : 'null',
                        'mime_type' => $imageFile ? $imageFile->getMimeType() : 'null'
                    ]);
                    throw new \Exception('Görsel yüklenemedi: ' . $e->getMessage());
                }
            }

            // Session ID yoksa oluştur
            if (!$sessionId) {
                $sessionId = Str::uuid()->toString();
            }

            // Sohbeti bul veya oluştur
            $conversation = Conversation::firstOrCreate(
                ['session_id' => $sessionId],
                ['last_message_at' => now()]
            );

            // Kullanıcı mesajını kaydet
            $userMessage = Message::create([
                'conversation_id' => $conversation->id,
                'type' => 'user',
                'content' => $message,
                'metadata' => [
                    'image_url' => $imageUrl,
                    'has_image' => !is_null($imageFile),
                ]
            ]);

            // ChatGPT'ye gönder (görsel varsa Vision API kullan)
            try {
                // Görsel varsa da normal sendMessage kullan (şimdilik Vision API kapalı)
                Log::info('Sending message to ChatGPT', [
                    'message_length' => strlen($message),
                    'has_image' => !is_null($imageFile)
                ]);

                // Görsel varsa mesaja bilgi ekle
                $fullMessage = $message;
                if ($imageFile) {
                    $fullMessage = $message . "\n\n[Kullanıcı bir görsel gönderdi: " . $imageFile->getClientOriginalName() . "]";
                }

                $response = $this->openAIService->sendMessage($fullMessage);
                Log::info('ChatGPT response received', ['response_length' => strlen($response)]);

                // Her zaman sesli cevap üret
                Log::info('Generating audio response for text message');
                $audioResponse = $this->elevenLabsService->textToSpeech($response);

                // Ses dosyasını kaydet
                $responseAudioPath = 'audio/responses/' . uniqid() . '.mp3';
                Storage::disk('public')->put($responseAudioPath, $audioResponse);
                $audioUrl = Storage::disk('public')->url($responseAudioPath);

                Log::info('Audio response generated', [
                    'audio_path' => $responseAudioPath,
                    'audio_url' => $audioUrl
                ]);

                // Vision API kodu (şimdilik yorumda)
                /*
                if ($imageFile) {
                    // Vision API için base64 encode
                    $imageData = base64_encode(file_get_contents($imageFile->getPathname()));
                    Log::info('Sending message with image to ChatGPT Vision API', [
                        'message_length' => strlen($message),
                        'image_size' => strlen($imageData),
                        'mime_type' => $mimeType
                    ]);
                    $response = $this->openAIService->sendMessageWithImage($message, $imageData, $mimeType);
                } else {
                    Log::info('Sending text message to ChatGPT', ['message' => $message]);
                    $response = $this->openAIService->sendMessage($message);
                }
                */
            } catch (\Exception $e) {
                Log::error('ChatGPT API call failed', [
                    'error' => $e->getMessage(),
                    'has_image' => !is_null($imageFile),
                    'message' => $message
                ]);
                throw $e;
            }

            // Asistan mesajını kaydet
            $assistantMessage = Message::create([
                'conversation_id' => $conversation->id,
                'type' => 'assistant',
                'content' => $response,
                'audio_url' => $audioUrl,
                'metadata' => [
                    'model' => 'gpt-4',
                    'response_to_image' => !is_null($imageFile),
                    'voice_id' => config('services.elevenlabs.voice_id'),
                ]
            ]);

            // Sohbet başlığını oluştur (ilk mesajsa)
            $conversation->generateTitle();
            $conversation->update(['last_message_at' => now()]);

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'message' => $message,
                'response' => $response,
                'audio_url' => $audioUrl,
                'image_url' => $imageUrl,
                'message_ids' => [
                    'user' => $userMessage->id,
                    'assistant' => $assistantMessage->id,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Chat message failed', [
                'error' => $e->getMessage(),
                'message' => $request->input('message', ''),
                'has_image' => $request->hasFile('image')
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Chat request failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
