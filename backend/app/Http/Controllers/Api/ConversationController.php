<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Http\Resources\ConversationResource;

class ConversationController extends Controller
{
    /**
     * Tüm sohbetleri listele
     */
    public function index(): JsonResponse
    {
        try {
            $conversations = Conversation::with(['lastMessage', 'messages'])
                ->orderByDesc('last_message_at')
                ->orderByDesc('updated_at')
                ->limit(50) // Son 50 sohbet
                ->get();

            return response()->json([
                'success' => true,
                'conversations' => ConversationResource::collection($conversations),
            ], 200, [], JSON_INVALID_UTF8_SUBSTITUTE);

        } catch (\Exception $e) {
            Log::error('Failed to fetch conversations', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Sohbet geçmişi alınamadı'
            ], 500);
        }
    }

    /**
     * Belirli bir sohbeti getir
     */
    public function show(string $sessionId): JsonResponse
    {
        try {
            $conversation = Conversation::where('session_id', $sessionId)
                ->with(['messages' => function ($query) {
                    $query->orderBy('created_at');
                }])
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'error' => 'Sohbet bulunamadı'
                ], 404);
            }

            $messages = $conversation->messages->map(function ($message) {
                return [
                    'id' => $message->id,
                    'type' => $message->type,
                    'text' => $message->content,
                    'audioUrl' => $message->audio_url,
                    'timestamp' => $message->created_at->timestamp,
                    'metadata' => $message->metadata,
                ];
            });

            return response()->json([
                'success' => true,
                'conversation' => [
                    'id' => $conversation->id,
                    'session_id' => $conversation->session_id,
                    'title' => $conversation->title,
                    'created_at' => $conversation->created_at->toISOString(),
                ],
                'messages' => $messages,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to fetch conversation', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Sohbet alınamadı'
            ], 500);
        }
    }

    /**
     * Belirli bir sohbetin mesajlarını getir
     */
    public function messages(string $sessionId): JsonResponse
    {
        try {
            $conversation = Conversation::where('session_id', $sessionId)
                ->with(['messages' => function ($query) {
                    $query->orderBy('created_at');
                }])
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'error' => 'Sohbet bulunamadı'
                ], 404);
            }

            $messages = $conversation->messages->map(function ($message) {
                return [
                    'id' => $message->id,
                    'type' => $message->type,
                    'text' => $message->content,
                    'audioUrl' => $message->audio_url,
                    'timestamp' => $message->created_at->timestamp,
                    'metadata' => $message->metadata,
                ];
            });

            return response()->json([
                'success' => true,
                'messages' => $messages
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch conversation messages', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Mesajlar alınamadı'
            ], 500);
        }
    }

    /**
     * Yeni sohbet oluştur
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $sessionId = Str::uuid()->toString();

            $conversation = Conversation::create([
                'session_id' => $sessionId,
                'last_message_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'conversation' => [
                    'id' => $conversation->id,
                    'session_id' => $conversation->session_id,
                    'title' => 'Yeni Sohbet',
                    'created_at' => $conversation->created_at->timestamp,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create conversation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Yeni sohbet oluşturulamadı'
            ], 500);
        }
    }

    /**
     * Sohbeti sil
     */
    public function destroy(string $sessionId): JsonResponse
    {
        try {
            $conversation = Conversation::where('session_id', $sessionId)->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'error' => 'Sohbet bulunamadı'
                ], 404);
            }

            $conversation->delete();

            return response()->json([
                'success' => true,
                'message' => 'Sohbet silindi'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete conversation', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Sohbet silinemedi'
            ], 500);
        }
    }
}
