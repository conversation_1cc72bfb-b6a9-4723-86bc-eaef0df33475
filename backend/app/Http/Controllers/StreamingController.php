<?php

namespace App\Http\Controllers;

use App\Services\OpenAIService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class StreamingController extends Controller
{
    protected $openaiService;

    public function __construct()
    {
        $this->openaiService = new OpenAIService();
    }

    /**
     * Stream chat response using Server-Sent Events
     */
    public function streamChat(Request $request): StreamedResponse
    {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        $message = $request->input('message');

        return new StreamedResponse(function () use ($message) {
            // Set headers for SSE
            header('Content-Type: text/event-stream');
            header('Cache-Control: no-cache');
            header('Connection: keep-alive');
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Headers: Cache-Control');

            // Send initial event
            echo "event: start\n";
            echo "data: " . json_encode(['message' => 'Yanıt oluşturuluyor...']) . "\n\n";
            ob_flush();
            flush();

            try {
                $fullResponse = $this->openaiService->sendMessageStream($message, function ($chunk) {
                    // Send each chunk as an SSE event
                    echo "event: chunk\n";
                    echo "data: " . json_encode(['content' => $chunk]) . "\n\n";
                    ob_flush();
                    flush();
                });

                // Send completion event
                echo "event: complete\n";
                echo "data: " . json_encode([
                    'message' => 'Yanıt tamamlandı',
                    'full_response' => $fullResponse
                ]) . "\n\n";
                ob_flush();
                flush();

            } catch (\Exception $e) {
                // Send error event
                echo "event: error\n";
                echo "data: " . json_encode([
                    'error' => $e->getMessage()
                ]) . "\n\n";
                ob_flush();
                flush();
            }

            // Send end event
            echo "event: end\n";
            echo "data: " . json_encode(['message' => 'Stream sona erdi']) . "\n\n";
            ob_flush();
            flush();
        });
    }

    /**
     * Simple chat endpoint (non-streaming)
     */
    public function chat(Request $request): JsonResponse
    {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        try {
            $response = $this->openaiService->sendMessage($request->input('message'));

            return response()->json([
                'success' => true,
                'response' => $response,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Chat request failed', [
                'error' => $e->getMessage(),
                'message' => $request->input('message')
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * WebSocket connection status
     */
    public function websocketStatus(): JsonResponse
    {
        try {
            $realtimeService = $this->openaiService->getRealtimeService();
            
            return response()->json([
                'websocket_available' => class_exists('Ratchet\Client\Connector'),
                'connected' => $realtimeService->isConnected(),
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'websocket_available' => false,
                'connected' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ]);
        }
    }

    /**
     * Test WebSocket connection
     */
    public function testWebSocket(Request $request): JsonResponse
    {
        $request->validate([
            'message' => 'string|max:1000'
        ]);

        $message = $request->input('message', 'Test mesajı');

        try {
            $realtimeService = $this->openaiService->getRealtimeService();
            
            // Set up event handlers for testing
            $responseReceived = false;
            $errorOccurred = false;
            $errorMessage = '';

            $realtimeService
                ->onConnect(function () {
                    Log::info('WebSocket test: Connected');
                })
                ->onMessage(function ($msg) use (&$responseReceived) {
                    Log::info('WebSocket test: Message received', ['type' => $msg['type'] ?? 'unknown']);
                    $responseReceived = true;
                })
                ->onError(function ($error) use (&$errorOccurred, &$errorMessage) {
                    Log::error('WebSocket test: Error', ['error' => $error->getMessage()]);
                    $errorOccurred = true;
                    $errorMessage = $error->getMessage();
                });

            // Try to connect
            $realtimeService->connect()
                ->then(function () use ($realtimeService, $message) {
                    sleep(1); // Wait for connection to stabilize
                    $realtimeService->sendTextMessage($message);
                    sleep(5); // Wait for response
                    $realtimeService->disconnect();
                })
                ->otherwise(function ($error) use (&$errorOccurred, &$errorMessage) {
                    $errorOccurred = true;
                    $errorMessage = $error->getMessage();
                });

            return response()->json([
                'success' => !$errorOccurred,
                'message_sent' => !$errorOccurred,
                'response_received' => $responseReceived,
                'error' => $errorOccurred ? $errorMessage : null,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('WebSocket test failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }
}
