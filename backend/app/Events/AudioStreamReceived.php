<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AudioStreamReceived implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $sessionId;
    public $transcription;
    public $responseText;
    public $audioUrl;

    public function __construct($sessionId, $transcription = null, $responseText = null, $audioUrl = null)
    {
        $this->sessionId = $sessionId;
        $this->transcription = $transcription;
        $this->responseText = $responseText;
        $this->audioUrl = $audioUrl;
    }

    public function broadcastOn()
    {
        return new Channel('audio.' . $this->sessionId);
    }
}
