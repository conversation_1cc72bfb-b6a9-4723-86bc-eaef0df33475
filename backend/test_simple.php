<?php

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel environment
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Starting simple test...\n";

try {
    echo "Creating OpenAI Realtime Service...\n";
    $service = new App\Services\OpenAIRealtimeService();
    echo "Service created successfully!\n";

    echo "Getting API key...\n";
    $apiKey = env('OPENAI_API_KEY');
    echo "API key exists: " . ($apiKey ? 'Yes' : 'No') . "\n";
    echo "API key length: " . strlen($apiKey) . "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "Test completed.\n";
