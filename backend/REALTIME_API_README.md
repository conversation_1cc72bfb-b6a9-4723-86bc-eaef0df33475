# OpenAI Realtime API WebSocket Integration

Bu proje OpenAI'nin Realtime API'sini WebSocket üzerinden kullanmak için gerekli servisleri içerir.

## Kurulum

### 1. <PERSON><PERSON><PERSON><PERSON>

```bash
composer require react/socket react/stream
```

### 2. Environment Değişkenlerini Ayarlayın

`.env` dosyanızda aşağıdaki değişkenlerin tanımlı olduğundan emin olun:

```env
OPENAI_API_KEY=your_openai_api_key_here
```

## Kullanım

### 1. Temel WebSocket Bağlantısı

```php
use App\Services\OpenAIRealtimeService;

$realtimeService = new OpenAIRealtimeService();

// Event handler'ları ayarlayın
$realtimeService
    ->onConnect(function () {
        echo "OpenAI Realtime API'ye bağlandı\n";
    })
    ->onMessage(function ($message) {
        echo "Mesaj alındı: " . json_encode($message) . "\n";
    })
    ->onError(function ($error) {
        echo "Hata: " . $error->getMessage() . "\n";
    });

// Bağlantıyı başlatın
$realtimeService->connect()
    ->then(function () use ($realtimeService) {
        // Bağlantı başarılı, mesaj gönderebilirsiniz
        $realtimeService->sendTextMessage("Merhaba!");
    });

// Event loop'u çalıştırın
$realtimeService->run();
```

### 2. Mevcut OpenAIService ile Entegrasyon

```php
use App\Services\OpenAIService;

$openaiService = new OpenAIService();

// Realtime bağlantısını başlatın
$openaiService->connectRealtime()
    ->then(function () use ($openaiService) {
        // Metin mesajı gönderin
        $openaiService->sendRealtimeText("Nasılsın?");
        
        // Ses verisi gönderin (base64 encoded PCM16)
        $audioData = base64_encode($pcm16AudioData);
        $openaiService->sendRealtimeAudio($audioData);
    });
```

### 3. HTTP API Endpoints

Aşağıdaki API endpoint'leri kullanabilirsiniz:

#### Bağlantı Kurma
```http
POST /api/realtime/connect
```

#### Metin Mesajı Gönderme
```http
POST /api/realtime/send-text
Content-Type: application/json

{
    "message": "Merhaba, nasılsın?"
}
```

#### Ses Verisi Gönderme
```http
POST /api/realtime/send-audio
Content-Type: application/json

{
    "audio": "base64_encoded_pcm16_audio_data"
}
```

#### Bağlantı Durumunu Kontrol Etme
```http
GET /api/realtime/status
```

#### Bağlantıyı Kesme
```http
POST /api/realtime/disconnect
```

### 4. Artisan Command ile Test

```bash
php artisan openai:test-realtime "Test mesajı"
```

## Özellikler

### Desteklenen Modaliteler
- **Metin**: Gerçek zamanlı metin mesajlaşması
- **Ses**: PCM16 formatında ses verisi gönderme ve alma

### Event Türleri

#### Gelen Events
- `session.created`: Oturum oluşturuldu
- `session.updated`: Oturum güncellendi
- `response.text.delta`: Streaming metin verisi
- `response.audio.delta`: Streaming ses verisi
- `response.done`: Yanıt tamamlandı
- `error`: Hata mesajı

#### Giden Events
- `session.update`: Oturum konfigürasyonu
- `conversation.item.create`: Yeni konuşma öğesi
- `response.create`: Yanıt oluşturma talebi
- `input_audio_buffer.append`: Ses verisi ekleme
- `input_audio_buffer.commit`: Ses buffer'ını commit etme

## Konfigürasyon

### Ses Ayarları
- **Input Format**: PCM16, 24kHz, mono
- **Output Format**: PCM16, 24kHz, mono
- **Voice**: alloy (varsayılan)

### Oturum Ayarları
- **Dil**: Türkçe
- **Temperature**: 0.8
- **Turn Detection**: Server VAD
- **Max Tokens**: Sınırsız

## Hata Yönetimi

Tüm WebSocket hataları otomatik olarak loglanır. Hata durumlarında:

1. Bağlantı hatalarını kontrol edin
2. API key'inizin geçerli olduğundan emin olun
3. Network bağlantınızı kontrol edin
4. OpenAI API limitlerini kontrol edin

## Güvenlik

- API key'inizi güvenli bir şekilde saklayın
- Rate limiting uygulanmıştır
- HTTPS/WSS bağlantıları kullanılır

## Sınırlamalar

- Eş zamanlı bağlantı sayısı sınırlıdır
- Ses verisi PCM16 formatında olmalıdır
- Maximum mesaj boyutu 1000 karakter
- API rate limiting uygulanır

## Örnek Kullanım Senaryoları

### 1. Sesli Asistan
```php
// Mikrofon verisini alın ve gönderin
$realtimeService->sendAudioData($microphoneData);
$realtimeService->commitAudioBuffer();
```

### 2. Gerçek Zamanlı Chat
```php
// Kullanıcı mesajını gönderin
$realtimeService->sendTextMessage($userMessage);
```

### 3. Ses-to-Ses Konuşma
```php
// Ses gönder ve ses yanıtı al
$realtimeService->sendAudioData($inputAudio);
// response.audio.delta event'lerinde yanıt sesini alın
```

## Troubleshooting

### Bağlantı Sorunları
- API key'inizi kontrol edin
- Network bağlantınızı test edin
- Firewall ayarlarını kontrol edin

### Ses Sorunları
- Ses formatının PCM16 olduğundan emin olun
- Sample rate'in 24kHz olduğunu kontrol edin
- Mono ses kullandığınızdan emin olun

### Performance
- Event loop'u bloke etmeyin
- Büyük ses dosyalarını chunk'lara bölün
- Memory kullanımını izleyin
