<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AudioController;
use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\ConversationController;
use App\Http\Controllers\RealtimeController;
use App\Http\Controllers\StreamingController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Audio processing routes
Route::prefix('audio')->middleware('throttle:10,1')->group(function () {
    Route::post('/process', [AudioController::class, 'processAudio']);
    Route::post('/stream', [AudioController::class, 'streamAudio']);
});

// Chat routes
Route::prefix('chat')->middleware('throttle:20,1')->group(function () {
    Route::post('/message', [ChatController::class, 'sendMessage']);
});

// Conversation routes
Route::prefix('conversations')->middleware('throttle:30,1')->group(function () {
    Route::get('/', [ConversationController::class, 'index']);
    Route::post('/', [ConversationController::class, 'store']);
    Route::get('/{sessionId}', [ConversationController::class, 'show']);
    Route::delete('/{sessionId}', [ConversationController::class, 'destroy']);
    Route::get('/{sessionId}/messages', [ConversationController::class, 'messages']);
});

// OpenAI Realtime API routes
Route::prefix('realtime')->middleware('throttle:20,1')->group(function () {
    Route::post('/connect', [RealtimeController::class, 'connect']);
    Route::post('/send-text', [RealtimeController::class, 'sendText']);
    Route::post('/send-audio', [RealtimeController::class, 'sendAudio']);
    Route::get('/status', [RealtimeController::class, 'status']);
    Route::post('/disconnect', [RealtimeController::class, 'disconnect']);
});

// Streaming Chat routes
Route::prefix('streaming')->middleware('throttle:30,1')->group(function () {
    Route::post('/chat', [StreamingController::class, 'chat']);
    Route::post('/stream-chat', [StreamingController::class, 'streamChat']);
    Route::get('/websocket-status', [StreamingController::class, 'websocketStatus']);
    Route::post('/test-websocket', [StreamingController::class, 'testWebSocket']);
});

// Health check
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'services' => [
            'openai' => !empty(env('OPENAI_API_KEY')),
            'elevenlabs' => !empty(env('ELEVENLABS_API_KEY')),
        ]
    ]);
});

// Additional route
Route::post('/stream-audio', [AudioController::class, 'streamAudio']);

