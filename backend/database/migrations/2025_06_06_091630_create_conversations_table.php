<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable(); // <PERSON><PERSON>bet başlığı (ilk mesajdan otomatik oluşturulacak)
            $table->string('session_id')->unique(); // Frontend session ID'si
            $table->timestamp('last_message_at')->nullable(); // Son mesaj zamanı
            $table->timestamps();

            $table->index(['session_id', 'last_message_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversations');
    }
};
